<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Flor para ti</title>
  <style>
    body {
      margin: 0;
      font-family: 'Segoe UI', sans-serif;
      background: radial-gradient(ellipse at bottom, #0d1b2a 0%, #000000 100%);
      height: 100vh;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .stars {
      position: absolute;
      width: 100%;
      height: 100%;
      background: transparent;
      box-shadow:
        50px 100px white, 120px 200px white, 200px 80px white,
        300px 150px white, 400px 250px white, 550px 90px white,
        600px 200px white, 750px 180px white, 900px 100px white,
        1050px 240px white, 1150px 150px white;
      animation: twinkle 3s infinite ease-in-out alternate;
      z-index: 0;
    }

    @keyframes twinkle {
      from { opacity: 0.3; }
      to { opacity: 1; }
    }

    .card {
      position: relative;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      padding: 40px 20px 60px;
      border-radius: 16px;
      box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
      text-align: center;
      max-width: 400px;
      backdrop-filter: blur(8px);
      color: white;
      z-index: 1;
    }

    .flower-container {
      position: relative;
      width: 200px;
      height: 300px;
      margin: 0 auto 40px;
    }

    .stem {
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 10px;
      height: 120px;
      background: linear-gradient(to bottom, #4caf50, #2e7d32);
      border-radius: 5px;
      transform: translateX(-50%);
      z-index: 0;
      box-shadow: 2px 0 4px rgba(0,0,0,0.3);
    }

    /* Hojas del tallo */
    .stem::before {
      content: '';
      position: absolute;
      top: 30px;
      left: -15px;
      width: 25px;
      height: 15px;
      background: linear-gradient(45deg, #4caf50, #66bb6a);
      border-radius: 0 100% 0 100%;
      transform: rotate(-30deg);
    }

    .stem::after {
      content: '';
      position: absolute;
      top: 50px;
      right: -15px;
      width: 25px;
      height: 15px;
      background: linear-gradient(-45deg, #4caf50, #66bb6a);
      border-radius: 100% 0 100% 0;
      transform: rotate(30deg);
    }

    .flower {
      position: absolute;
      top: 0;
      left: 50%;
      width: 180px;
      height: 180px;
      transform: translateX(-50%);
      animation: gentleFloat 6s ease-in-out infinite;
    }

    @keyframes gentleFloat {
      0%, 100% { transform: translateX(-50%) translateY(0px) rotate(0deg); }
      50% { transform: translateX(-50%) translateY(-5px) rotate(2deg); }
    }

    .petal {
      position: absolute;
      width: 60px;
      height: 80px;
      background: linear-gradient(135deg, #ff6b9d, #ff8fab, #ffa8c0);
      border-radius: 80% 15% 80% 15%;
      top: 50%;
      left: 50%;
      transform-origin: 50% 90%;
      box-shadow:
        inset 0 0 20px rgba(255, 255, 255, 0.3),
        0 2px 8px rgba(255, 107, 157, 0.4);
      animation: petalGlow 4s ease-in-out infinite alternate;
    }

    @keyframes petalGlow {
      from {
        box-shadow:
          inset 0 0 20px rgba(255, 255, 255, 0.3),
          0 2px 8px rgba(255, 107, 157, 0.4);
      }
      to {
        box-shadow:
          inset 0 0 25px rgba(255, 255, 255, 0.5),
          0 4px 12px rgba(255, 107, 157, 0.6);
      }
    }

    /* Pétalos con diferentes rotaciones y ligeras variaciones */
    .petal:nth-child(1)  {
      transform: rotate(0deg) translateY(-50px);
      background: linear-gradient(135deg, #ff6b9d, #ff8fab);
    }
    .petal:nth-child(2)  {
      transform: rotate(45deg) translateY(-50px);
      background: linear-gradient(135deg, #ff8fab, #ffa8c0);
    }
    .petal:nth-child(3)  {
      transform: rotate(90deg) translateY(-50px);
      background: linear-gradient(135deg, #ff6b9d, #ff9bb5);
    }
    .petal:nth-child(4)  {
      transform: rotate(135deg) translateY(-50px);
      background: linear-gradient(135deg, #ff8fab, #ffb3c6);
    }
    .petal:nth-child(5)  {
      transform: rotate(180deg) translateY(-50px);
      background: linear-gradient(135deg, #ff6b9d, #ff8fab);
    }
    .petal:nth-child(6)  {
      transform: rotate(225deg) translateY(-50px);
      background: linear-gradient(135deg, #ff9bb5, #ffa8c0);
    }
    .petal:nth-child(7)  {
      transform: rotate(270deg) translateY(-50px);
      background: linear-gradient(135deg, #ff6b9d, #ffb3c6);
    }
    .petal:nth-child(8)  {
      transform: rotate(315deg) translateY(-50px);
      background: linear-gradient(135deg, #ff8fab, #ff9bb5);
    }

    .center {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 35px;
      height: 35px;
      background: radial-gradient(circle, #ffd54f, #ffb300, #ff8f00);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      box-shadow:
        0 0 20px rgba(255, 213, 79, 0.8),
        inset 0 0 10px rgba(255, 179, 0, 0.6);
      z-index: 2;
    }

    /* Pequeños detalles en el centro */
    .center::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      background: radial-gradient(circle, #fff59d, #ffcc02);
      border-radius: 50%;
      transform: translate(-50%, -50%);
    }

    .center::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 8px;
      height: 8px;
      background: #ff8f00;
      border-radius: 50%;
      transform: translate(-50%, -50%);
      box-shadow:
        3px 0 0 #ff8f00,
        -3px 0 0 #ff8f00,
        0 3px 0 #ff8f00,
        0 -3px 0 #ff8f00;
    }

    .message {
      font-size: 1.3rem;
      color: #fff;
      margin-bottom: 10px;
    }

    .signature {
      font-weight: bold;
      color: #ff4081;
      font-size: 1.2rem;
    }
  </style>
</head>
<body>
  <div class="stars"></div>

  <div class="card">
    <div class="flower-container">
      <div class="stem"></div>
      <div class="flower">
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="center"></div>
      </div>
    </div>
    <div class="message">Puedes con todo, no lo olvides</div>
    <div class="signature">B❤️</div>
  </div>
</body>
</html>