<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Flor para ti</title>
  <style>
    body {
      margin: 0;
      font-family: 'Segoe UI', sans-serif;
      background: radial-gradient(ellipse at bottom, #0d1b2a 0%, #000000 100%);
      height: 100vh;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .stars {
      position: absolute;
      width: 100%;
      height: 100%;
      background: transparent;
      box-shadow:
        50px 100px white, 120px 200px white, 200px 80px white,
        300px 150px white, 400px 250px white, 550px 90px white,
        600px 200px white, 750px 180px white, 900px 100px white,
        1050px 240px white, 1150px 150px white;
      animation: twinkle 3s infinite ease-in-out alternate;
      z-index: 0;
    }

    @keyframes twinkle {
      from { opacity: 0.3; }
      to { opacity: 1; }
    }

    .card {
      position: relative;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      padding: 40px 20px 60px;
      border-radius: 16px;
      box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
      text-align: center;
      max-width: 400px;
      backdrop-filter: blur(8px);
      color: white;
      z-index: 1;
    }

    .flower-container {
      position: relative;
      width: 200px;
      height: 300px;
      margin: 0 auto 40px;
    }

    .stem {
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 10px;
      height: 120px;
      background: linear-gradient(to bottom, #4caf50, #2e7d32);
      border-radius: 5px;
      transform: translateX(-50%);
      z-index: 0;
      box-shadow: 2px 0 4px rgba(0,0,0,0.3);
    }

    /* Hojas del tallo */
    .stem::before {
      content: '';
      position: absolute;
      top: 30px;
      left: -15px;
      width: 25px;
      height: 15px;
      background: linear-gradient(45deg, #4caf50, #66bb6a);
      border-radius: 0 100% 0 100%;
      transform: rotate(-30deg);
    }

    .stem::after {
      content: '';
      position: absolute;
      top: 50px;
      right: -15px;
      width: 25px;
      height: 15px;
      background: linear-gradient(-45deg, #4caf50, #66bb6a);
      border-radius: 100% 0 100% 0;
      transform: rotate(30deg);
    }

    .flower {
      position: absolute;
      top: 20px;
      left: 50%;
      width: 160px;
      height: 160px;
      transform: translateX(-50%);
      animation: gentleFloat 8s ease-in-out infinite;
    }

    @keyframes gentleFloat {
      0%, 100% { transform: translateX(-50%) translateY(0px) rotate(0deg); }
      50% { transform: translateX(-50%) translateY(-3px) rotate(1deg); }
    }

    .petal {
      position: absolute;
      width: 50px;
      height: 70px;
      background: linear-gradient(45deg, #ff69b4, #ff1493, #dc143c);
      border-radius: 50% 10% 50% 10%;
      top: 50%;
      left: 50%;
      transform-origin: center bottom;
      box-shadow:
        0 2px 10px rgba(255, 20, 147, 0.4),
        inset 0 0 15px rgba(255, 255, 255, 0.2);
      animation: petalSway 6s ease-in-out infinite;
    }

    @keyframes petalSway {
      0%, 100% { transform: translate(-50%, -50%) scale(1); }
      50% { transform: translate(-50%, -50%) scale(1.05); }
    }

    /* Posicionamiento de pétalos en círculo */
    .petal:nth-child(1) {
      transform: translate(-50%, -50%) rotate(0deg) translateY(-40px) rotate(-0deg);
      background: linear-gradient(45deg, #ff69b4, #ff1493);
      animation-delay: 0s;
    }
    .petal:nth-child(2) {
      transform: translate(-50%, -50%) rotate(45deg) translateY(-40px) rotate(-45deg);
      background: linear-gradient(45deg, #ff1493, #dc143c);
      animation-delay: 0.2s;
    }
    .petal:nth-child(3) {
      transform: translate(-50%, -50%) rotate(90deg) translateY(-40px) rotate(-90deg);
      background: linear-gradient(45deg, #ff69b4, #ff20b2);
      animation-delay: 0.4s;
    }
    .petal:nth-child(4) {
      transform: translate(-50%, -50%) rotate(135deg) translateY(-40px) rotate(-135deg);
      background: linear-gradient(45deg, #ff1493, #e91e63);
      animation-delay: 0.6s;
    }
    .petal:nth-child(5) {
      transform: translate(-50%, -50%) rotate(180deg) translateY(-40px) rotate(-180deg);
      background: linear-gradient(45deg, #ff69b4, #ff1493);
      animation-delay: 0.8s;
    }
    .petal:nth-child(6) {
      transform: translate(-50%, -50%) rotate(225deg) translateY(-40px) rotate(-225deg);
      background: linear-gradient(45deg, #dc143c, #c2185b);
      animation-delay: 1s;
    }
    .petal:nth-child(7) {
      transform: translate(-50%, -50%) rotate(270deg) translateY(-40px) rotate(-270deg);
      background: linear-gradient(45deg, #ff69b4, #ff20b2);
      animation-delay: 1.2s;
    }
    .petal:nth-child(8) {
      transform: translate(-50%, -50%) rotate(315deg) translateY(-40px) rotate(-315deg);
      background: linear-gradient(45deg, #ff1493, #e91e63);
      animation-delay: 1.4s;
    }

    .center {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 30px;
      height: 30px;
      background: radial-gradient(circle, #ffd700, #ffb347, #ff8c00);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      box-shadow:
        0 0 15px rgba(255, 215, 0, 0.7),
        inset 0 0 8px rgba(255, 140, 0, 0.5);
      z-index: 10;
      animation: centerGlow 3s ease-in-out infinite alternate;
    }

    @keyframes centerGlow {
      from {
        box-shadow:
          0 0 15px rgba(255, 215, 0, 0.7),
          inset 0 0 8px rgba(255, 140, 0, 0.5);
      }
      to {
        box-shadow:
          0 0 25px rgba(255, 215, 0, 0.9),
          inset 0 0 12px rgba(255, 140, 0, 0.7);
      }
    }

    .center::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 15px;
      height: 15px;
      background: radial-gradient(circle, #fff8dc, #ffd700);
      border-radius: 50%;
      transform: translate(-50%, -50%);
    }

    .message {
      font-size: 1.3rem;
      color: #fff;
      margin-bottom: 10px;
    }

    .signature {
      font-weight: bold;
      color: #ff4081;
      font-size: 1.2rem;
    }
  </style>
</head>
<body>
  <div class="stars"></div>

  <div class="card">
    <div class="flower-container">
      <div class="stem"></div>
      <div class="flower">
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="center"></div>
      </div>
    </div>
    <div class="message">Puedes con todo, no lo olvides</div>
    <div class="signature">B❤️</div>
  </div>
</body>
</html>