<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Flor para ti</title>
  <style>
    body {
      margin: 0;
      font-family: 'Segoe UI', sans-serif;
      background: radial-gradient(ellipse at bottom, #0d1b2a 0%, #000000 100%);
      height: 100vh;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .stars {
      position: absolute;
      width: 100%;
      height: 100%;
      background: transparent;
      box-shadow:
        50px 100px white, 120px 200px white, 200px 80px white,
        300px 150px white, 400px 250px white, 550px 90px white,
        600px 200px white, 750px 180px white, 900px 100px white,
        1050px 240px white, 1150px 150px white;
      animation: twinkle 3s infinite ease-in-out alternate;
      z-index: 0;
    }

    @keyframes twinkle {
      from { opacity: 0.3; }
      to { opacity: 1; }
    }

    .card {
      position: relative;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      padding: 40px 20px 60px;
      border-radius: 16px;
      box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
      text-align: center;
      max-width: 400px;
      backdrop-filter: blur(8px);
      color: white;
      z-index: 1;
    }

    .flower-container {
      position: relative;
      width: 200px;
      height: 300px;
      margin: 0 auto 40px;
    }

    .stem {
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 8px;
      height: 100px;
      background: linear-gradient(to bottom, #4caf50, #2e7d32);
      border-radius: 4px;
      transform: translateX(-50%);
      z-index: 0;
    }

    .flower {
      position: absolute;
      top: 0;
      left: 50%;
      width: 160px;
      height: 160px;
      transform: translateX(-50%);
      animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
      from { transform: translateX(-50%) rotate(0deg); }
      to { transform: translateX(-50%) rotate(360deg); }
    }

    .petal {
      position: absolute;
      width: 80px;
      height: 30px;
      background: white;
      border-radius: 50% 50% 50% 50%;
      top: 50%;
      left: 50%;
      transform-origin: center left;
    }

    .petal:nth-child(1)  { transform: rotate(0deg)   translateX(40px); }
    .petal:nth-child(2)  { transform: rotate(45deg)  translateX(40px); }
    .petal:nth-child(3)  { transform: rotate(90deg)  translateX(40px); }
    .petal:nth-child(4)  { transform: rotate(135deg) translateX(40px); }
    .petal:nth-child(5)  { transform: rotate(180deg) translateX(40px); }
    .petal:nth-child(6)  { transform: rotate(225deg) translateX(40px); }
    .petal:nth-child(7)  { transform: rotate(270deg) translateX(40px); }
    .petal:nth-child(8)  { transform: rotate(315deg) translateX(40px); }

    .center {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 45px;
      height: 45px;
      background: radial-gradient(circle, #fdd835, #f9a825);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      box-shadow: 0 0 15px #fdd835aa;
      z-index: 1;
    }

    .message {
      font-size: 1.3rem;
      color: #fff;
      margin-bottom: 10px;
    }

    .signature {
      font-weight: bold;
      color: #ff4081;
      font-size: 1.2rem;
    }
  </style>
</head>
<body>
  <div class="stars"></div>

  <div class="card">
    <div class="flower-container">
      <div class="stem"></div>
      <div class="flower">
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="petal"></div>
        <div class="center"></div>
      </div>
    </div>
    <div class="message">Puedes con todo, no lo olvides</div>
    <div class="signature">B❤️</div>
  </div>
</body>
</html>