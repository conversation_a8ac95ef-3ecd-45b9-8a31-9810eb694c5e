<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title><PERSON></title>
  <style>
    body {
      margin: 0;
      font-family: 'Georgia', serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }

    .container {
      text-align: center;
      background: rgba(255, 255, 255, 0.1);
      padding: 40px;
      border-radius: 20px;
      backdrop-filter: blur(10px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .rose {
      position: relative;
      width: 200px;
      height: 250px;
      margin: 0 auto 30px;
    }

    /* Tallo */
    .stem {
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 6px;
      height: 120px;
      background: linear-gradient(to bottom, #228B22, #006400);
      border-radius: 3px;
      transform: translateX(-50%);
      box-shadow: 2px 0 4px rgba(0, 0, 0, 0.3);
    }

    /* Hojas */
    .leaf {
      position: absolute;
      width: 30px;
      height: 20px;
      background: linear-gradient(45deg, #32CD32, #228B22);
      border-radius: 0 100% 0 100%;
      bottom: 40px;
    }

    .leaf.left {
      left: 85px;
      transform: rotate(-30deg);
    }

    .leaf.right {
      right: 85px;
      transform: rotate(30deg) scaleX(-1);
    }

    /* Flor */
    .flower {
      position: absolute;
      top: 0;
      left: 50%;
      width: 120px;
      height: 120px;
      transform: translateX(-50%);
      animation: gentleSway 8s ease-in-out infinite;
    }

    @keyframes gentleSway {
      0%, 100% { transform: translateX(-50%) rotate(0deg); }
      25% { transform: translateX(-50%) rotate(1deg); }
      75% { transform: translateX(-50%) rotate(-1deg); }
    }

    /* Pétalos exteriores */
    .petal-outer {
      position: absolute;
      width: 40px;
      height: 50px;
      background: linear-gradient(45deg, #FF69B4, #FF1493, #DC143C);
      border-radius: 50% 10% 50% 10%;
      top: 50%;
      left: 50%;
      transform-origin: 50% 90%;
      box-shadow: 0 2px 8px rgba(220, 20, 60, 0.4);
    }

    .petal-outer:nth-child(1) { transform: translate(-50%, -50%) rotate(0deg) translateY(-30px); }
    .petal-outer:nth-child(2) { transform: translate(-50%, -50%) rotate(60deg) translateY(-30px); }
    .petal-outer:nth-child(3) { transform: translate(-50%, -50%) rotate(120deg) translateY(-30px); }
    .petal-outer:nth-child(4) { transform: translate(-50%, -50%) rotate(180deg) translateY(-30px); }
    .petal-outer:nth-child(5) { transform: translate(-50%, -50%) rotate(240deg) translateY(-30px); }
    .petal-outer:nth-child(6) { transform: translate(-50%, -50%) rotate(300deg) translateY(-30px); }

    /* Pétalos interiores */
    .petal-inner {
      position: absolute;
      width: 30px;
      height: 35px;
      background: linear-gradient(45deg, #FFB6C1, #FF69B4, #FF1493);
      border-radius: 50% 10% 50% 10%;
      top: 50%;
      left: 50%;
      transform-origin: 50% 90%;
      box-shadow: 0 1px 4px rgba(255, 105, 180, 0.5);
    }

    .petal-inner:nth-child(7) { transform: translate(-50%, -50%) rotate(30deg) translateY(-20px); }
    .petal-inner:nth-child(8) { transform: translate(-50%, -50%) rotate(90deg) translateY(-20px); }
    .petal-inner:nth-child(9) { transform: translate(-50%, -50%) rotate(150deg) translateY(-20px); }
    .petal-inner:nth-child(10) { transform: translate(-50%, -50%) rotate(210deg) translateY(-20px); }
    .petal-inner:nth-child(11) { transform: translate(-50%, -50%) rotate(270deg) translateY(-20px); }
    .petal-inner:nth-child(12) { transform: translate(-50%, -50%) rotate(330deg) translateY(-20px); }

    /* Centro de la rosa */
    .center {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      background: radial-gradient(circle, #8B0000, #DC143C);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      box-shadow: 0 0 10px rgba(139, 0, 0, 0.6);
      z-index: 10;
    }

    /* Texto */
    .message {
      color: white;
      font-size: 1.4rem;
      margin-bottom: 15px;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .signature {
      color: #FFB6C1;
      font-size: 1.2rem;
      font-weight: bold;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    }

    /* Efectos de partículas */
    .sparkle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: white;
      border-radius: 50%;
      animation: sparkle 3s ease-in-out infinite;
    }

    @keyframes sparkle {
      0%, 100% { opacity: 0; transform: scale(0); }
      50% { opacity: 1; transform: scale(1); }
    }

    .sparkle:nth-child(1) { top: 20%; left: 20%; animation-delay: 0s; }
    .sparkle:nth-child(2) { top: 30%; right: 25%; animation-delay: 1s; }
    .sparkle:nth-child(3) { bottom: 40%; left: 30%; animation-delay: 2s; }
    .sparkle:nth-child(4) { bottom: 20%; right: 20%; animation-delay: 0.5s; }
    .sparkle:nth-child(5) { top: 60%; left: 15%; animation-delay: 1.5s; }
  </style>
</head>
<body>
  <div class="container">
    <div class="rose">
      <!-- Tallo y hojas -->
      <div class="stem"></div>
      <div class="leaf left"></div>
      <div class="leaf right"></div>
      
      <!-- Flor -->
      <div class="flower">
        <!-- Pétalos exteriores -->
        <div class="petal-outer"></div>
        <div class="petal-outer"></div>
        <div class="petal-outer"></div>
        <div class="petal-outer"></div>
        <div class="petal-outer"></div>
        <div class="petal-outer"></div>
        
        <!-- Pétalos interiores -->
        <div class="petal-inner"></div>
        <div class="petal-inner"></div>
        <div class="petal-inner"></div>
        <div class="petal-inner"></div>
        <div class="petal-inner"></div>
        <div class="petal-inner"></div>
        
        <!-- Centro -->
        <div class="center"></div>
      </div>
      
      <!-- Efectos de brillo -->
      <div class="sparkle"></div>
      <div class="sparkle"></div>
      <div class="sparkle"></div>
      <div class="sparkle"></div>
      <div class="sparkle"></div>
    </div>
    
    <div class="message">Una rosa para alegrarte el día</div>
    <div class="signature">Con cariño ✨</div>
  </div>
</body>
</html>
